<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZG2EDQW47V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-ZG2EDQW47V');
    </script>
    <title>Valorant Random Slot Picker | Choose Your Players</title>
    <meta name="description"
        content="Free Valorant random slot picker tool. Randomly select 10 players from 22 available slots for custom matches. Created by Shushie.">
    <meta name="keywords"
        content="valorant, valorant slots, random slot picker, valorant random player, valorant tool, gaming tool, slot selector, valorant slot randomizer">
    <meta name="author" content="Shushie">
    <meta property="og:title" content="Valorant Random Slot Picker">
    <meta property="og:description"
        content="Free tool to randomly select 10 players from 22 slots for your Valorant custom matches.">
    <meta property="og:type" content="website">
    <meta name="robots" content="index, follow">
    <link rel="stylesheet" href="slot-picker-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@400;500;700&display=swap" rel="stylesheet">
</head>

<body>
    <div class="container">
        <div class="logo">
            <h1 class="valorant-title">VALORANT</h1>
        </div>
        <h1>RANDOM PLAYER PICKER</h1>

        <div class="main-layout">
            <div class="content-area">
                <div class="lobby-container">
                    <!-- Main Game Slots -->
                    <div class="game-slots">
                        <!-- Players Section -->
                        <div class="team-section players-slots">
                            <div class="slots-grid">
                                <div class="slot" data-slot="1">
                                    <div class="slot-content">
                                        <div class="player-name">Player 1</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="2">
                                    <div class="slot-content">
                                        <div class="player-name">Player 2</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="3">
                                    <div class="slot-content">
                                        <div class="player-name">Player 3</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="4">
                                    <div class="slot-content">
                                        <div class="player-name">Player 4</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="5">
                                    <div class="slot-content">
                                        <div class="player-name">Player 5</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Players Section -->
                        <div class="team-section players-slots">
                            <div class="slots-grid">
                                <div class="slot" data-slot="6">
                                    <div class="slot-content">
                                        <div class="player-name">Player 6</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="7">
                                    <div class="slot-content">
                                        <div class="player-name">Player 7</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="8">
                                    <div class="slot-content">
                                        <div class="player-name">Player 8</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="9">
                                    <div class="slot-content">
                                        <div class="player-name">Player 9</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                                <div class="slot" data-slot="10">
                                    <div class="slot-content">
                                        <div class="player-name">Player 10</div>
                                        <div class="slot-status">Ready</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Players List Section -->
                        <div class="team-section players-list">
                            <h2 class="team-title">PLAYERS LIST</h2>

                            <!-- Player Input Section -->
                            <div class="player-input-section">
                                <div class="input-group">
                                    <input type="text" id="player-name-input" placeholder="Enter player name..." maxlength="20">
                                    <button id="add-player-btn" class="val-btn small">ADD</button>
                                    <button id="paste-player-btn" class="val-btn small secondary">PASTE</button>
                                </div>
                                <div class="player-count">
                                    <span id="player-list-count">0 players added</span>
                                    <button id="clear-players-btn" class="val-btn small secondary">CLEAR ALL</button>
                                </div>
                            </div>

                            <!-- Players Display -->
                            <div class="players-display">
                                <div id="players-container" class="players-grid">
                                    <!-- Players will be dynamically added here -->
                                </div>
                                <div id="empty-players-message" class="empty-message">
                                    Add player names to get started
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="controls">
                    <button id="pick-random-btn" class="val-btn">PICK RANDOM 10 PLAYERS</button>
                    <button id="reset-btn" class="val-btn secondary">RESET SELECTION</button>
                    <button id="fill-afk-btn" class="val-btn secondary">FILL AFK SLOTS</button>
                    <button id="clear-afk-btn" class="val-btn secondary">CLEAR AFK</button>
                </div>

                <div class="selection-info">
                    <p id="selection-count">Select 10 players to start the match</p>
                </div>
            </div>

            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>Map Picker</h3>
                    <a href="https://valormp.onrender.com" class="switch-version" target="_blank">Try the Map
                        Picker!</a>
                </div>

                <div class="sidebar-section">
                    <h3>Creator</h3>
                    <div class="creator-info">
                        <p>Created by <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1"
                                target="_blank" rel="noopener noreferrer">Shushie</a></p>
                        <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" target="_blank"
                            rel="noopener noreferrer" class="youtube-link">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="currentColor">
                                <path
                                    d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                            </svg>
                            Subscribe on YouTube
                        </a>
                    </div>
                </div>

                <div class="sidebar-section" id="shortcuts-container">
                    <h3>Controls</h3>
                    <!-- Shortcut box will be moved here by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="slot-picker-script.js"></script>
</body>

</html>
