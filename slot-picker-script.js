/**
 * Valorant Random Slot Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shu<PERSON><PERSON>_valorant)
 *
 * A tool for randomly selecting 10 players from a custom players list for Valorant custom matches.
 * Players are distributed evenly between Attackers and Defenders teams.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const pickRandomBtn = document.getElementById('pick-random-btn');
    const resetBtn = document.getElementById('reset-btn');
    const selectionCount = document.getElementById('selection-count');
    const allSlots = document.querySelectorAll('.slot');

    // Players List Elements
    const playerNameInput = document.getElementById('player-name-input');
    const addPlayerBtn = document.getElementById('add-player-btn');
    const pastePlayerBtn = document.getElementById('paste-player-btn');
    const clearPlayersBtn = document.getElementById('clear-players-btn');
    const playersContainer = document.getElementById('players-container');
    const playerListCount = document.getElementById('player-list-count');
    const emptyPlayersMessage = document.getElementById('empty-players-message');

    // Game state
    let selectedSlots = new Set();
    let playersList = [];
    const playersToSelect = 10;

    /**
     * Updates the selection count display
     */
    function updateSelectionCount() {
        const count = selectedSlots.size;
        const playersCount = playersList.length;

        if (count === 0) {
            if (playersCount < playersToSelect) {
                selectionCount.textContent = `Add ${playersToSelect - playersCount} more players to start`;
                selectionCount.style.color = 'var(--valorant-light)';
            } else {
                selectionCount.textContent = 'Click "PICK RANDOM 10 PLAYERS" to start the match';
                selectionCount.style.color = 'var(--valorant-light)';
            }
        } else if (count === playersToSelect) {
            selectionCount.textContent = `${count}/${playersToSelect} players selected - Ready to start!`;
            selectionCount.style.color = 'var(--valorant-selected)';
        }
    }

    /**
     * Updates the players list count display
     */
    function updatePlayersListCount() {
        const count = playersList.length;
        playerListCount.textContent = `${count} player${count !== 1 ? 's' : ''} added`;

        // Show/hide empty message
        if (count === 0) {
            emptyPlayersMessage.style.display = 'block';
            playersContainer.style.display = 'none';
        } else {
            emptyPlayersMessage.style.display = 'none';
            playersContainer.style.display = 'grid';
        }

        updateSelectionCount();
    }

    /**
     * Adds a player to the players list
     * @param {string} playerName - The name of the player to add
     */
    function addPlayer(playerName) {
        const trimmedName = playerName.trim();
        if (!trimmedName) return false;

        // Check for duplicates
        if (playersList.some(player => player.toLowerCase() === trimmedName.toLowerCase())) {
            alert('Player already exists in the list!');
            return false;
        }

        playersList.push(trimmedName);
        renderPlayersList();
        updatePlayersListCount();
        return true;
    }

    /**
     * Removes a player from the players list
     * @param {number} index - The index of the player to remove
     */
    function removePlayer(index) {
        if (index >= 0 && index < playersList.length) {
            playersList.splice(index, 1);
            renderPlayersList();
            updatePlayersListCount();
        }
    }

    /**
     * Clears all players from the list
     */
    function clearAllPlayers() {
        if (playersList.length === 0) return;

        if (confirm('Are you sure you want to clear all players?')) {
            playersList = [];
            renderPlayersList();
            updatePlayersListCount();
            clearSelections();
        }
    }

    /**
     * Renders the players list in the UI (newest first)
     */
    function renderPlayersList() {
        playersContainer.innerHTML = '';

        // Reverse the array to show newest players first
        const reversedPlayers = [...playersList].reverse();

        reversedPlayers.forEach((playerName, reverseIndex) => {
            // Calculate the original index for removal
            const originalIndex = playersList.length - 1 - reverseIndex;
            const playerItem = document.createElement('div');
            playerItem.className = 'player-item';
            playerItem.innerHTML = `
                <span class="player-item-name">${playerName}</span>
                <button class="remove-player-btn" onclick="removePlayer(${originalIndex})" title="Remove player">×</button>
            `;
            playersContainer.appendChild(playerItem);
        });
    }

    /**
     * Clears all current selections
     */
    function clearSelections() {
        selectedSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('selected');
            const status = slot.querySelector('.slot-status');
            const playerNameDiv = slot.querySelector('.player-name');
            status.textContent = 'Ready';
            // Reset to default player names
            const slotNumber = parseInt(slot.dataset.slot);
            playerNameDiv.textContent = `Player ${slotNumber}`;
        });
        updateSelectionCount();
    }

    /**
     * Adds selection animation to a slot
     * @param {HTMLElement} slot - The slot element to animate
     * @param {string} playerName - The player name to assign
     * @param {number} delay - Animation delay in milliseconds
     */
    function animateSlotSelection(slot, playerName, delay = 0) {
        setTimeout(() => {
            slot.classList.add('selected');
            const status = slot.querySelector('.slot-status');
            const playerNameDiv = slot.querySelector('.player-name');
            status.textContent = 'Selected';
            playerNameDiv.textContent = playerName;

            // Add a brief highlight effect
            slot.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slot.style.transform = 'scale(1)';
            }, 200);
        }, delay);
    }

    /**
     * Picks random players from the list and assigns them to slots
     */
    function pickRandomSlots() {
        // Check if we have enough players
        if (playersList.length < playersToSelect) {
            alert(`Not enough players! Need ${playersToSelect} players but only ${playersList.length} are available. Add more players to the list.`);
            return;
        }

        // Clear previous selections
        clearSelections();

        // Disable button during animation
        pickRandomBtn.disabled = true;
        pickRandomBtn.textContent = 'SELECTING...';

        // Shuffle the players list using Fisher-Yates algorithm
        const shuffledPlayers = [...playersList];
        for (let i = shuffledPlayers.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffledPlayers[i], shuffledPlayers[j]] = [shuffledPlayers[j], shuffledPlayers[i]];
        }

        // Select first 10 players from shuffled array
        const selectedPlayers = shuffledPlayers.slice(0, playersToSelect);

        // Assign players to slots (10 total slots)
        const allSlots = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

        // Animate selections with staggered timing
        selectedPlayers.forEach((playerName, index) => {
            const slotNumber = allSlots[index];

            const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
            if (slot) {
                selectedSlots.add(slotNumber);
                // Stagger animations for visual effect
                animateSlotSelection(slot, playerName, index * 150);
            }
        });

        // Update UI after all animations
        setTimeout(() => {
            updateSelectionCount();
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';

            // Update page title for better UX
            document.title = `${selectedSlots.size} Players Selected - Valorant Random Slot Picker | by Shushie`;

        }, selectedPlayers.length * 150 + 200);
    }

    /**
     * Handles paste functionality
     */
    async function handlePaste() {
        try {
            const text = await navigator.clipboard.readText();
            const trimmedText = text.trim();
            if (trimmedText) {
                if (addPlayer(trimmedText)) {
                    playerNameInput.value = '';
                }
            }
        } catch (err) {
            // Fallback for browsers that don't support clipboard API
            const pastedText = prompt('Paste player name:');
            if (pastedText && pastedText.trim()) {
                if (addPlayer(pastedText.trim())) {
                    playerNameInput.value = '';
                }
            }
        }
    }

    /**
     * Handles adding player from input field
     */
    function handleAddPlayer() {
        const playerName = playerNameInput.value.trim();
        if (playerName) {
            if (addPlayer(playerName)) {
                playerNameInput.value = '';
            }
        }
    }

    // Make removePlayer function global so it can be called from HTML
    window.removePlayer = removePlayer;

    // Event Listeners
    pickRandomBtn.addEventListener('click', pickRandomSlots);
    resetBtn.addEventListener('click', clearSelections);

    // Players list event listeners
    addPlayerBtn.addEventListener('click', handleAddPlayer);
    pastePlayerBtn.addEventListener('click', handlePaste);
    clearPlayersBtn.addEventListener('click', clearAllPlayers);

    // Input field event listeners
    playerNameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleAddPlayer();
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !pickRandomBtn.disabled) {
            e.preventDefault();
            pickRandomSlots();
        } else if (e.code === 'Escape') {
            clearSelections();
        }
    });

    // Initialize
    updateSelectionCount();
    updatePlayersListCount();

    // Add keyboard shortcuts info to sidebar
    const shortcutsContainer = document.getElementById('shortcuts-container');
    if (shortcutsContainer) {
        const shortcutInfo = document.createElement('div');
        shortcutInfo.className = 'shortcut-box';
        shortcutInfo.innerHTML = `
            <div class="shortcut-title">Keyboard Shortcuts:</div>
            <div class="shortcut-item">SPACE - Random Pick</div>
            <div class="shortcut-item">ESC - Reset Selection</div>
            <div class="shortcut-item">ENTER - Add Player</div>
        `;
        shortcutsContainer.appendChild(shortcutInfo);
    }
});
